import { NextRequest, NextResponse } from 'next/server'
import { loadProviderConfig } from '@bmad/core/src/config-loader'
import { chooseProvider } from '@bmad/core/src/router'
import { writeAuditEvent } from '@bmad/core/src/audit'
import { hashContent } from './route-helper'

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const messages = (body?.messages || []) as { role: string; content: string }[]
    const explicitModel = (body?.model as string | undefined)

    const config = await loadProviderConfig(process.env.PROVIDERS_CONFIG)

    // If client specifies model, find the provider that exposes it; otherwise route by defaults
    let provider = undefined
    if (explicitModel) {
      provider = config.providers.find(p => (p.models || []).some(m => m.name === explicitModel) || p.model === explicitModel)
    }
    if (!provider) {
      provider = chooseProvider(config, { task: 'chat' })
    }
    if (!provider) throw new Error('No provider available')

    if (provider.kind !== 'ollama') throw new Error('Only ollama is supported in local mode scaffold')

    const base = provider.base_url || process.env.OLLAMA_BASE_URL || 'http://localhost:11434'

    // Simple non-stream call using /api/chat (Ollama format)
    const resp = await fetch(`${base}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: explicitModel || provider.model || provider.models?.[0]?.name,
        messages
      })
    })
    if (!resp.ok) {
      const text = await resp.text()
      throw new Error(`ollama error: ${resp.status} ${text}`)
    }
    const data = await resp.json()

    // Ollama returns response in 'message.content' or streaming deltas; here we use final content
    const reply = data?.message?.content || ''

    // audit (metadata-only)
    await writeAuditEvent(config, {
      timestamp: new Date().toISOString(),
      model_id: explicitModel || provider.model || provider.models?.[0]?.name,
      route_decision: provider.id,
      input_hash: hashContent(JSON.stringify(messages).slice(0, 4096)),
      output_hash: hashContent(reply)
    })

    return NextResponse.json({ reply })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'failed' }, { status: 500 })
  }
}
