'use client'
import React, { useState } from 'react'
import useS<PERSON> from 'swr'
import AgentPicker from '../../components/AgentPicker'

const fetcher = (url: string) => fetch(url).then(r => r.json())

type ModelItem = { name: string }
type Agent = { id: string; title: string }

type Message = {
  role: 'user' | 'assistant'
  content: string
}

export default function ChatPage() {
  const { data } = useSWR<{ models: ModelItem[] }>(`/api/models`, fetcher)
  const { data: agentsData } = useSWR<{ agents: Agent[] }>(`/api/agents`, fetcher)
  const [model, setModel] = useState<string>('')
  const [input, setInput] = useState('')
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)

  const models = data?.models || []
  const agents = agentsData?.agents || []
  const [selectedAgents, setSelectedAgents] = useState<string[]>([])

  const send = async () => {
    const text = input.trim()
    if (!text) return
    setLoading(true)
    setMessages(m => [...m, { role: 'user', content: text }])
    setInput('')
    try {
      const res = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, { role: 'user', content: text }],
          model: model || undefined,
          agents: selectedAgents
        })
      })
      const json = await res.json()
      setMessages(m => [...m, { role: 'assistant', content: json.reply || '' }])
    } catch (e: any) {
      setMessages(m => [...m, { role: 'assistant', content: 'Error: ' + (e?.message || 'unknown') }])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <div className="mb-3">
        <AgentPicker agents={agents} value={selectedAgents} onChange={setSelectedAgents} />
      </div>

      <div className="mb-3">
        <label>Model:&nbsp;</label>
        <select value={model} onChange={e => setModel(e.target.value)}>
          <option value="">auto (router)</option>
          {models.map(m => (
            <option key={m.name} value={m.name}>{m.name}</option>
          ))}
        </select>
      </div>

      <div className="mb-3">
        <div>
          {messages.map((m, i) => (
            <div key={i} className={`message ${m.role}`}>
              <b>{m.role}:</b> {m.content}
            </div>
          ))}
        </div>
      </div>

      <div className="mb-3">
        <textarea value={input} onChange={e => setInput(e.target.value)} placeholder="Type here..." />
      </div>

      <div>
        <button onClick={send} disabled={loading}>{loading ? 'Sending...' : 'Send'}</button>
      </div>
    </div>
  )
}
