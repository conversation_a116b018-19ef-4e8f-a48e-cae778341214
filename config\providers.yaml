# providers.yaml - Pure local deployment (Ollama) with placeholders for SSO (Google Workspace) and LINE channel
# This file defines model providers, routing, auditing, and auth channel stubs for the Web GUI/server to consume.
# Replace environment placeholders in your runtime (.env) or secret manager.

version: 1

defaults:
  logging:
    audit_enabled: true         # emit structured audit events
    level: metadata-only        # content | metadata-only | off
    pii_redaction: true         # redact PII before any persistence
  security:
    iam:
      enforce_rbac: true        # enforce role-based access at the app layer
  network:
    egress_restrictions: true   # app should restrict outbound traffic by default

providers:
  - id: local-ollama
    kind: ollama
    base_url: http://localhost:11434
    # Optional: if running behind another host or container network, update base_url accordingly
    # base_url: http://ollama:11434
    # GPU/VRAM balancing for RTX 2060 6GB: use low_vram and smaller ctx/batch to mix CPU+GPU memory gracefully
    ollama:
      options:
        low_vram: true        # reduce VRAM pressure by keeping more on CPU RAM
        num_ctx: 2048         # reduce context window for memory balance (adjust upwards if stable)
        num_batch: 64         # smaller batch uses less VRAM
        num_gpu: 1            # use one GPU
        main_gpu: 0           # which GPU index
        f16_kv: true          # mixed precision for KV cache
        use_mmap: true        # map model from disk (lower RSS)
        use_mlock: false      # avoid locking in RAM on desktop OS
    models:
      - name: llama3.1:8b-instruct-q4_K_M
        labels: [default, code, reasoning, quick]
        capabilities: { tool_use: partial, long_context: limited }
      - name: qwen2.5:7b-instruct-q4_K_M
        labels: [code, fast, draft]
        capabilities: { tool_use: partial, long_context: limited }
      # You can add other local models here once pulled by Ollama (ollama pull <model>)
    constraints:
      max_input_tokens: 8000
      max_output_tokens: 2048
    policy:
      offline_only: true
      store_prompts: false
      store_completions: false

routing:
  rules:
    - when:
        sensitivity: high
      use: local-ollama
    - when:
        task: quick_draft
      prefer: local-ollama
  default: local-ollama

audit:
  sink:
    - type: file
      # The app should ensure this path exists and is writable by the process
      path: ./logs/ai-audit/audit.log
  fields:
    - timestamp
    - user_id
    - user_email
    - agent_id
    - model_id
    - token_usage
    - latency_ms
    - route_decision
    - input_hash
    - output_hash
retention:
  logs_days: 180
  pii_storage: none

# Authentication/Channels (stubs referenced by the application layer; not used by the providers directly)
auth:
  sso:
    provider: google
    allowed_domains:
      - your-company.com
    client_id_env: GOOGLE_OAUTH_CLIENT_ID
    client_secret_env: GOOGLE_OAUTH_CLIENT_SECRET
    callback_url_env: GOOGLE_OAUTH_CALLBACK_URL   # e.g. http://localhost:3000/api/auth/callback/google
  channels:
    line:
      enabled: true
      channel_id_env: LINE_CHANNEL_ID
      channel_secret_env: LINE_CHANNEL_SECRET
      channel_access_token_env: LINE_CHANNEL_ACCESS_TOKEN
      webhook_path: /api/webhooks/line
      # The app should verify signature header 'X-Line-Signature' against channel secret
