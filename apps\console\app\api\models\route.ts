import { NextRequest, NextResponse } from 'next/server'
import { loadProviderConfig } from '@bmad/core/src/config-loader'

export async function GET(req: NextRequest) {
  try {
    const config = await loadProviderConfig(process.env.PROVIDERS_CONFIG)
    const models = [] as { name: string }[]

    for (const p of config.providers) {
      if (p.models && Array.isArray(p.models)) {
        for (const m of p.models) models.push({ name: m.name })
      }
      if (p.model) models.push({ name: p.model })
    }

    return NextResponse.json({ models })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'failed' }, { status: 500 })
  }
}
