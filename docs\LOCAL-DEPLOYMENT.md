# Pure Local Deployment (Ollama) with Google Workspace SSO and LINE Integration

This guide describes how to run a pure-local deployment using Ollama for model inference, integrate Google Workspace SSO for authentication, and enable a LINE channel webhook.

Contents
- Prerequisites
- Option A: docker-compose (recommended for dev)
- Option B: systemd services (for Linux hosts)
- Google Workspace SSO setup
- LINE Messaging API webhook setup
- Environment variables

## Prerequisites
- Linux/macOS host (Windows WSL2 is fine)
- Docker + docker compose plugin (for Option A) OR systemd (for Option B)
- Open ports (default):
  - 11434 (Ollama)
  - 3000 (Web GUI frontend/back-end, example)
- Edit config/providers.yaml as needed

## Option A: docker-compose
Create a file at `deploy/docker-compose.yml`:

```yaml
version: "3.8"
services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    environment:
      - OLLAMA_KEEP_ALIVE=1h

  # app example (placeholder)
  app:
    image: yourorg/bmad-web:latest
    container_name: bmad-web
    depends_on:
      - ollama
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - ../.env
    volumes:
      - ../config/providers.yaml:/app/config/providers.yaml:ro
    environment:
      - PROVIDERS_CONFIG=/app/config/providers.yaml
      - OLLAMA_BASE_URL=http://ollama:11434

volumes:
  ollama-data:
```

Bring up the stack:

```bash
mkdir -p deploy
# Save the YAML above into deploy/docker-compose.yml
cd deploy && docker compose up -d
# Pull models (examples):
docker exec -it ollama ollama pull llama3.1:8b-instruct-q4_K_M
# Optionally
# docker exec -it ollama ollama pull qwen2.5:7b-instruct-q4_K_M
```

## Option B: systemd services
Create a unit file for Ollama (root or a system admin account):

`/etc/systemd/system/ollama.service`
```ini
[Unit]
Description=Ollama Local LLM Server
After=network.target

[Service]
Type=simple
User=ollama
Group=ollama
Environment=OLLAMA_KEEP_ALIVE=1h
ExecStart=/usr/local/bin/ollama serve
Restart=always
RestartSec=5
RuntimeMaxSec=0

[Install]
WantedBy=multi-user.target
```

- Create user/group and install binary:
```bash
sudo useradd --system --create-home --shell /usr/sbin/nologin ollama || true
# Install ollama (follow official docs) and place binary at /usr/local/bin/ollama
sudo systemctl daemon-reload
sudo systemctl enable --now ollama
# Pull models as the ollama user
sudo -u ollama ollama pull llama3.1:8b-instruct-q4_K_M
```

Create an app service (placeholder):

`/etc/systemd/system/bmad-web.service`
```ini
[Unit]
Description=BMad Local Web App
After=network.target ollama.service

[Service]
Type=simple
User=bmad
Group=bmad
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=PROVIDERS_CONFIG=/opt/bmad/config/providers.yaml
Environment=OLLAMA_BASE_URL=http://127.0.0.1:11434
EnvironmentFile=/opt/bmad/.env
WorkingDirectory=/opt/bmad/app
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Create user, dirs, and start:
```bash
sudo useradd --system --create-home --shell /usr/sbin/nologin bmad || true
sudo mkdir -p /opt/bmad/{app,config,logs}
# Place your app build into /opt/bmad/app, server entry = server.js
sudo cp ./config/providers.yaml /opt/bmad/config/providers.yaml
sudo chown -R bmad:bmad /opt/bmad
sudo systemctl daemon-reload
sudo systemctl enable --now bmad-web
```

## Google Workspace SSO setup (OAuth 2.0)
1. In Google Cloud Console (same Workspace org), create OAuth 2.0 Client Credentials (Web Application).
2. Authorized redirect URI: `http://localhost:3000/api/auth/callback/google` (adjust for prod domain).
3. Set environment variables:
   - `GOOGLE_OAUTH_CLIENT_ID`
   - `GOOGLE_OAUTH_CLIENT_SECRET`
   - `GOOGLE_OAUTH_CALLBACK_URL`
   - Optionally enforce allowed domains via `config/providers.yaml` -> `auth.sso.allowed_domains`.
4. The Web app should verify `hd` claim (hosted domain) or check the user’s email domain.

## LINE Messaging API webhook setup
1. Create a LINE Messaging API channel.
2. Set Channel secret and issue Channel access token (long-lived).
3. Webhook URL: `https://<your-domain>/api/webhooks/line`
4. Set environment variables in `.env`:
   - `LINE_CHANNEL_ID`
   - `LINE_CHANNEL_SECRET`
   - `LINE_CHANNEL_ACCESS_TOKEN`
5. App must verify the `X-Line-Signature` header using the Channel secret.

## Environment variables (.env)
```bash
# SSO (Google)
GOOGLE_OAUTH_CLIENT_ID=...
GOOGLE_OAUTH_CLIENT_SECRET=...
GOOGLE_OAUTH_CALLBACK_URL=http://localhost:3000/api/auth/callback/google

# LINE
LINE_CHANNEL_ID=...
LINE_CHANNEL_SECRET=...
LINE_CHANNEL_ACCESS_TOKEN=...

# App
PROVIDERS_CONFIG=./config/providers.yaml
OLLAMA_BASE_URL=http://localhost:11434
PORT=3000
```

Notes
- This repository provides providers.yaml and documentation; the server/client skeleton will consume it in Step 1.
- For ISO 27001 alignment on local:
  - Limit access by OS users/groups, rotate tokens, restrict log content to metadata where possible.
  - Keep audit logs in `./logs/` or system logs with logrotate retention (180 days per policy).
