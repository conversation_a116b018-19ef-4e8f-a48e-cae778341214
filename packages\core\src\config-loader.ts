import fs from 'fs-extra'
import yaml from 'js-yaml'
import path from 'path'
import { ProviderConfig } from './types'

export async function loadProviderConfig(customPath?: string): Promise<ProviderConfig> {
  const base = process.cwd()
  const candidates = [
    customPath,
    process.env.PROVIDERS_CONFIG,
    path.join(base, 'config', 'providers.yaml')
  ].filter(Boolean) as string[]

  for (const p of candidates) {
    try {
      const raw = await fs.readFile(p, 'utf8')
      const data = yaml.load(raw) as ProviderConfig
      if (!data || !data.providers) throw new Error('Invalid providers.yaml: missing providers')
      return data
    } catch (e) {
      // try next candidate
    }
  }
  throw new Error('providers.yaml not found. Provide PROVIDERS_CONFIG or place config/providers.yaml')
}
