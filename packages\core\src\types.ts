export type ProviderKind = 'ollama' | 'vertex' | 'openai' | 'anthropic';

export interface ProviderModel {
  name: string;
  labels?: string[];
  capabilities?: Record<string, any>;
}

export interface ProviderEntry {
  id: string;
  kind: ProviderKind;
  base_url?: string;
  project_id?: string;
  location?: string;
  model?: string;
  models?: ProviderModel[];
  policy?: Record<string, any>;
  constraints?: {
    max_input_tokens?: number;
    max_output_tokens?: number;
  };
}

export interface RoutingRule {
  when?: Record<string, any>;
  use?: string;
  prefer?: string;
  fallback?: string;
}

export interface ProviderConfig {
  version?: number;
  defaults?: Record<string, any>;
  providers: ProviderEntry[];
  routing?: {
    rules?: RoutingRule[];
    default?: string;
  };
  audit?: Record<string, any>;
  retention?: Record<string, any>;
  auth?: Record<string, any>;
}
