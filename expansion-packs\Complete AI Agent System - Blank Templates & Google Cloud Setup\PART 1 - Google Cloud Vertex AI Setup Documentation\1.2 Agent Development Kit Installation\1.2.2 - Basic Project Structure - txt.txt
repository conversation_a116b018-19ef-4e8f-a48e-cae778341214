{{company_name}}-ai-agents/
├── agents/
│   ├── __init__.py
│   ├── {{team_1}}/
│   │   ├── __init__.py
│   │   ├── {{agent_1}}.py
│   │   └── {{agent_2}}.py
│   └── {{team_2}}/
├── tasks/
│   ├── __init__.py
│   ├── {{task_category_1}}/
│   └── {{task_category_2}}/
├── templates/
│   ├── {{document_type_1}}/
│   └── {{document_type_2}}/
├── checklists/
├── data/
├── workflows/
├── config/
│   ├── settings.py
│   └── agent_config.yaml
├── main.py
└── deployment/
    ├── Dockerfile
    └── cloudbuild.yaml