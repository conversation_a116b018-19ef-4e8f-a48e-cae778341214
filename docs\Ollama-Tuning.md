# Ollama Tuning for RTX 2060 6GB

This repo ships a pure-local setup using Ollama. To balance GPU VRAM and system RAM on a 6GB card (RTX 2060), use smaller context and batch sizes, and enable mixed-precision KV.

Key options
- low_vram: true — keep more state in system RAM
- num_ctx: 2048 — reduce context window
- num_batch: 64 — smaller batch reduces VRAM pressure
- num_gpu: 1, main_gpu: 0 — single GPU selection
- f16_kv: true (KV cache half precision)
- use_mmap: true — lower RSS by mapping weights from disk

Where to set
- config/providers.yaml (ollama.options)
- deploy/docker-compose.yml (environment variables)

Notes
- For larger prompts, consider q4 quantized models and keep num_ctx <= 2048.
- If OOM occurs, lower num_batch to 32 or 16, or num_ctx to 1024.
- On Windows/WSL, shared memory and disk speed can impact latency; prefer SSD.
