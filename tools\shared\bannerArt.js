// ASCII banner art definitions extracted from banners.js to separate art from logic

const BMAD_TITLE = "BMAD-METHOD";
const FLATTENER_TITLE = "FLATTENER";
const INSTALLER_TITLE = "INSTALLER";

// Large ASCII blocks (block-style fonts)
const BMAD_LARGE = `
██████╗ ███╗   ███╗ █████╗ ██████╗       ███╗   ███╗███████╗████████╗██╗  ██╗ ██████╗ ██████╗ 
██╔══██╗████╗ ████║██╔══██╗██╔══██╗      ████╗ ████║██╔════╝╚══██╔══╝██║  ██║██╔═══██╗██╔══██╗
██████╔╝██╔████╔██║███████║██║  ██║█████╗██╔████╔██║█████╗     ██║   ███████║██║   ██║██║  ██║
██╔══██╗██║╚██╔╝██║██╔══██║██║  ██║╚════╝██║╚██╔╝██║██╔══╝     ██║   ██╔══██║██║   ██║██║  ██║
██████╔╝██║ ╚═╝ ██║██║  ██║██████╔╝      ██║ ╚═╝ ██║███████╗   ██║   ██║  ██║╚██████╔╝██████╔╝
╚═════╝ ╚═╝     ╚═╝╚═╝  ╚═╝╚═════╝       ╚═╝     ╚═╝╚══════╝   ╚═╝   ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ 
`;

const FLATTENER_LARGE = `
███████╗██╗      █████╗ ████████╗████████╗███████╗███╗   ██╗███████╗██████╗ 
██╔════╝██║     ██╔══██╗╚══██╔══╝╚══██╔══╝██╔════╝████╗  ██║██╔════╝██╔══██╗
█████╗  ██║     ███████║   ██║      ██║   █████╗  ██╔██╗ ██║█████╗  ██████╔╝
██╔══╝  ██║     ██╔══██║   ██║      ██║   ██╔══╝  ██║╚██╗██║██╔══╝  ██╔══██╗
██║     ███████║██║  ██║   ██║      ██║   ███████╗██║ ╚████║███████╗██║  ██║
╚═╝     ╚══════╝╚═╝  ╚═╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝
 `;

const INSTALLER_LARGE = `
██╗███╗   ██╗███████╗████████╗ █████╗ ██╗     ██╗     ███████╗██████╗ 
██║████╗  ██║██╔════╝╚══██╔══╝██╔══██╗██║     ██║     ██╔════╝██╔══██╗
██║██╔██╗ ██║███████╗   ██║   ███████║██║     ██║     █████╗  ██████╔╝
██║██║╚██╗██║╚════██║   ██║   ██╔══██║██║     ██║     ██╔══╝  ██╔══██╗
██║██║ ╚████║███████║   ██║   ██║  ██║███████╗███████╗███████╗██║  ██║
╚═╝╚═╝  ╚═══╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝╚═╝  ╚═╝
`;

// Curated medium/small/tiny variants (fixed art, no runtime scaling)
// Medium: bold framed title with heavy fill (high contrast, compact)
const BMAD_MEDIUM = `
███╗ █╗    █╗ ██╗ ███╗    █╗    █╗███╗█████╗█╗ █╗ ██╗ ███╗ 
█╔═█╗██╗  ██║█╔═█╗█╔═█╗   ██╗  ██║█╔═╝╚═█╔═╝█║ █║█╔═█╗█╔═█╗
███╔╝█╔███╔█║████║█║ █║██╗█╔███╔█║██╗   █║  ████║█║ █║█║ █║
█╔═█╗█║ █╔╝█║█╔═█║█║ █║╚═╝█║ █╔╝█║█╔╝   █║  █╔═█║█║ █║█║ █║
███╔╝█║ ╚╝ █║█║ █║███╔╝   █║ ╚╝ █║███╗  █║  █║ █║╚██╔╝███╔╝
╚══╝ ╚╝    ╚╝╚╝ ╚╝╚══╝    ╚╝    ╚╝╚══╝  ╚╝  ╚╝ ╚╝ ╚═╝ ╚══╝ 
`;

const FLATTENER_MEDIUM = `
███╗█╗   ██╗ █████╗█████╗███╗█╗  █╗███╗███╗ 
█╔═╝█║  █╔═█╗╚═█╔═╝╚═█╔═╝█╔═╝██╗ █║█╔═╝█╔═█╗
██╗ █║  ████║  █║    █║  ██╗ █╔█╗█║██╗ ███╔╝
█╔╝ █║  █╔═█║  █║    █║  █╔╝ █║ ██║█╔╝ █╔═█╗
█║  ███║█║ █║  █║    █║  ███╗█║  █║███╗█║ █║
╚╝  ╚══╝╚╝ ╚╝  ╚╝    ╚╝  ╚══╝╚╝  ╚╝╚══╝╚╝ ╚╝
 `;

const INSTALLER_MEDIUM = `
█╗█╗  █╗████╗█████╗ ██╗ █╗  █╗  ███╗███╗ 
█║██╗ █║█╔══╝╚═█╔═╝█╔═█╗█║  █║  █╔═╝█╔═█╗
█║█╔█╗█║████╗  █║  ████║█║  █║  ██╗ ███╔╝
█║█║ ██║╚══█║  █║  █╔═█║█║  █║  █╔╝ █╔═█╗
█║█║  █║████║  █║  █║ █║███╗███╗███╗█║ █║
╚╝╚╝  ╚╝╚═══╝  ╚╝  ╚╝ ╚╝╚══╝╚══╝╚══╝╚╝ ╚╝
`;

// Small: rounded box with bold rule
// Width: 30 columns total (28 inner)
const BMAD_SMALL = `
╭──────────────────────────╮
│        BMAD-METHOD       │
╰──────────────────────────╯
`;

const FLATTENER_SMALL = `
╭──────────────────────────╮
│         FLATTENER        │
╰──────────────────────────╯
`;

const INSTALLER_SMALL = `
 ╭──────────────────────────╮
 │         INSTALLER        │
 ╰──────────────────────────╯
 `;

// Tiny (compact brackets)
const BMAD_TINY = `[ BMAD-METHOD ]`;
const FLATTENER_TINY = `[ FLATTENER ]`;
const INSTALLER_TINY = `[ INSTALLER ]`;

module.exports = {
  BMAD_TITLE,
  FLATTENER_TITLE,
  INSTALLER_TITLE,
  BMAD_LARGE,
  FLATTENER_LARGE,
  INSTALLER_LARGE,
  BMAD_MEDIUM,
  FLATTENER_MEDIUM,
  INSTALLER_MEDIUM,
  BMAD_SMALL,
  FLATTENER_SMALL,
  INSTALLER_SMALL,
  BMAD_TINY,
  FLATTENER_TINY,
  INSTALLER_TINY,
};
