name: Discord Notification

on: [pull_request, release, create, delete, issue_comment, pull_request_review, pull_request_review_comment]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Notify Discord
        uses: sarisia/actions-status-discord@v1
        if: always()
        with:
          webhook: ${{ secrets.DISCORD_WEBHOOK }}
          status: ${{ job.status }}
          title: "Triggered by ${{ github.event_name }}"
          color: 0x5865F2