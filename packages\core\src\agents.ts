import fs from 'fs-extra'
import path from 'path'
import yaml from 'js-yaml'

export interface AgentInfo {
  id: string
  title: string
  filePath: string
}

const candidateDirs = [
  // running from apps/console
  path.resolve(process.cwd(), '../../bmad-core/agents'),
  path.resolve(process.cwd(), 'bmad-core/agents'),
  path.resolve(process.cwd(), '.bmad-core/agents')
]

export async function listAgents(): Promise<AgentInfo[]> {
  let dir: string | null = null
  for (const d of candidateDirs) {
    if (await fs.pathExists(d)) { dir = d; break }
  }
  if (!dir) return []
  const files = (await fs.readdir(dir)).filter(f => f.endsWith('.md'))
  const agents: AgentInfo[] = []
  for (const f of files) {
    const p = path.join(dir, f)
    const id = f.replace(/\.md$/, '')
    try {
      const content = await fs.readFile(p, 'utf8')
      const match = content.match(/```ya?ml\s*\n([\s\S]*?)```/i)
      let title = id
      if (match) {
        try {
          const data = yaml.load(match[1]) as any
          if (data && typeof data.title === 'string') {
            title = String(data.title)
          }
        } catch {}
      }
      agents.push({ id, title, filePath: p })
    } catch {}
  }
  return agents.sort((a, b) => a.title.localeCompare(b.title))
}
