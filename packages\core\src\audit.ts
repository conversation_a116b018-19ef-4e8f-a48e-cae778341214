import fs from 'fs-extra'
import path from 'path'
import { ProviderConfig } from './types'

export interface AuditEvent {
  timestamp: string
  user_id?: string
  user_email?: string
  agent_id?: string
  model_id?: string
  token_usage?: { prompt?: number; completion?: number; total?: number }
  latency_ms?: number
  route_decision?: string
  input_hash?: string
  output_hash?: string
}

export async function writeAuditEvent(config: ProviderConfig, event: AuditEvent) {
  const sinks = (config as any)?.audit?.sink || []
  for (const s of sinks) {
    if (s.type === 'file' && s.path) {
      const filePath = path.resolve(process.cwd(), s.path)
      await fs.ensureDir(path.dirname(filePath))
      await fs.appendFile(filePath, JSON.stringify(event) + '\n')
    }
    // Future: add gcp-logging sink, etc.
  }
}
