name: Release
'on':
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      version_type:
        description: Version bump type
        required: true
        default: patch
        type: choice
        options:
          - patch
          - minor
          - major
permissions:
  contents: write
  issues: write
  pull-requests: write
  packages: write
jobs:
  release:
    runs-on: ubuntu-latest
    if: '!contains(github.event.head_commit.message, ''[skip ci]'')'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: npm
          registry-url: https://registry.npmjs.org
      - name: Install dependencies
        run: npm ci
      - name: Run tests and validation
        run: |
          npm run validate
          npm run format
      - name: Debug permissions
        run: |
          echo "Testing git permissions..."
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          echo "Git config set successfully"
      - name: Manual version bump
        if: github.event_name == 'workflow_dispatch'
        run: npm run version:${{ github.event.inputs.version_type }}
      - name: Semantic Release
        if: github.event_name == 'push'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: npm run release
