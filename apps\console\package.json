{"name": "bmad-console", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "postinstall": "node -e \"require('fs').mkdirSync('./public', { recursive: true })\"", "build": "next build", "start": "next start -p 3000", "lint": "echo 'no lint configured'"}, "dependencies": {"autoprefixer": "10.4.19", "fs-extra": "11.3.0", "js-yaml": "4.1.0", "next": "14.2.5", "next-auth": "5.0.0-beta.20", "postcss": "8.4.39", "react": "18.3.1", "react-dom": "18.3.1", "swr": "2.2.5", "tailwindcss": "3.4.10"}, "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "20.12.12", "@types/react": "18.2.79", "@types/react-dom": "18.2.25", "typescript": "5.5.4"}}