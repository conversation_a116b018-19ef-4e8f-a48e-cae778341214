version: "3.8"
services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    environment:
      - OLLAMA_KEEP_ALIVE=1h
      - OLLAMA_NUM_GPU=1
      - OLLAMA_NUM_CTX=2048
      - OLLAMA_NUM_BATCH=64
      - OLLAMA_KV_CACHE_TYPE=f16
    volumes:
      - ollama-data:/root/.ollama

  bmad-console:
    image: node:20-alpine
    container_name: bmad-console
    working_dir: /app
    depends_on:
      - ollama
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ../:/app
    environment:
      - NODE_ENV=development
      - OLLAMA_BASE_URL=http://ollama:11434
      - PROVIDERS_CONFIG=/app/config/providers.yaml
      - LINE_CHANNEL_ID=${LINE_CHANNEL_ID}
      - LINE_CHANNEL_SECRET=${LINE_CHANNEL_SECRET}
      - LINE_CHANNEL_ACCESS_TOKEN=${LINE_CHANNEL_ACCESS_TOKEN}
      - APP_BASE_URL=http://localhost:3000
    command: sh -c "cd apps/console && npm i && npm run dev"

volumes:
  ollama-data:
