'use client'
import React from 'react'

export type Agent = { id: string; title: string }

export default function AgentPicker({ agents, value, onChange }: {
  agents: Agent[]
  value: string[]
  onChange: (ids: string[]) => void
}) {
  const toggle = (id: string) => {
    if (value.includes(id)) onChange(value.filter(v => v !== id))
    else onChange([...value, id])
  }

  return (
    <div>
      <label className="block mb-1 font-medium">Agents</label>
      <div className="grid grid-cols-2 gap-1">
        {agents.map(a => (
          <label key={a.id} className="flex items-center gap-2">
            <input type="checkbox" checked={value.includes(a.id)} onChange={() => toggle(a.id)} />
            <span title={a.id}>{a.title}</span>
          </label>
        ))}
      </div>
    </div>
  )
}
