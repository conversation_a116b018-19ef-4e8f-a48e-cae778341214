/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/page";
exports.ids = ["app/chat/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/chat/page.tsx */ \"(rsc)/./app/chat/page.tsx\")), \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/chat/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/page\",\n        pathname: \"/chat\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/chat/page.tsx */ \"(ssr)/./app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNCTUFELU1FVEhPRC1tYWluJTVDJTVDYXBwcyU1QyU1Q2NvbnNvbGUlNUMlNUNhcHAlNUMlNUNjaGF0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUE2RiIsInNvdXJjZXMiOlsid2VicGFjazovL2JtYWQtY29uc29sZS8/Mjc1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXEJNQUQtTUVUSE9ELW1haW5cXFxcYXBwc1xcXFxjb25zb2xlXFxcXGFwcFxcXFxjaGF0XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CBMAD-METHOD-main%5C%5Capps%5C%5Cconsole%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/chat/page.tsx":
/*!***************************!*\
  !*** ./app/chat/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/core/index.mjs\");\n/* harmony import */ var _components_AgentPicker__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/AgentPicker */ \"(ssr)/./components/AgentPicker.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst fetcher = (url)=>fetch(url).then((r)=>r.json());\nfunction ChatPage() {\n    const { data } = (0,swr__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(`/api/models`, fetcher);\n    const { data: agentsData } = (0,swr__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(`/api/agents`, fetcher);\n    const [model, setModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const models = data?.models || [];\n    const agents = agentsData?.agents || [];\n    const [selectedAgents, setSelectedAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const send = async ()=>{\n        const text = input.trim();\n        if (!text) return;\n        setLoading(true);\n        setMessages((m)=>[\n                ...m,\n                {\n                    role: \"user\",\n                    content: text\n                }\n            ]);\n        setInput(\"\");\n        try {\n            const res = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    messages: [\n                        ...messages,\n                        {\n                            role: \"user\",\n                            content: text\n                        }\n                    ],\n                    model: model || undefined,\n                    agents: selectedAgents\n                })\n            });\n            const json = await res.json();\n            setMessages((m)=>[\n                    ...m,\n                    {\n                        role: \"assistant\",\n                        content: json.reply || \"\"\n                    }\n                ]);\n        } catch (e) {\n            setMessages((m)=>[\n                    ...m,\n                    {\n                        role: \"assistant\",\n                        content: \"Error: \" + (e?.message || \"unknown\")\n                    }\n                ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentPicker__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    agents: agents,\n                    value: selectedAgents,\n                    onChange: setSelectedAgents\n                }, void 0, false, {\n                    fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        children: \"Model:\\xa0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: model,\n                        onChange: (e)=>setModel(e.target.value),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"auto (router)\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            models.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: m.name,\n                                    children: m.name\n                                }, m.name, false, {\n                                    fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: messages.map((m, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `message ${m.role}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: [\n                                        m.role,\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                m.content\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: input,\n                    onChange: (e)=>setInput(e.target.value),\n                    placeholder: \"Type here...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: send,\n                    disabled: loading,\n                    children: loading ? \"Sending...\" : \"Send\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/chat/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/AgentPicker.tsx":
/*!************************************!*\
  !*** ./components/AgentPicker.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AgentPicker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AgentPicker({ agents, value, onChange }) {\n    const toggle = (id)=>{\n        if (value.includes(id)) onChange(value.filter((v)=>v !== id));\n        else onChange([\n            ...value,\n            id\n        ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block mb-1 font-medium\",\n                children: \"Agents\"\n            }, void 0, false, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\components\\\\AgentPicker.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-1\",\n                children: agents.map((a)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: value.includes(a.id),\n                                onChange: ()=>toggle(a.id)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\components\\\\AgentPicker.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                title: a.id,\n                                children: a.title\n                            }, void 0, false, {\n                                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\components\\\\AgentPicker.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, a.id, true, {\n                        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\components\\\\AgentPicker.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\components\\\\AgentPicker.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\components\\\\AgentPicker.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AgentPicker.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0689218fe30f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibWFkLWNvbnNvbGUvLi9hcHAvZ2xvYmFscy5jc3M/OGZlNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA2ODkyMThmZTMwZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/chat/page.tsx":
/*!***************************!*\
  !*** ./app/chat/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\BMAD-METHOD-main\apps\console\app\chat\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\BMAD-METHOD-main\apps\console\app\chat\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst metadata = {\n    title: \"BMad Console\",\n    description: \"Local LLM console (Ollama) with Google SSO and LINE webhook\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"BMad Console\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\layout.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\BMAD-METHOD-main\\\\apps\\\\console\\\\app\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQjtBQUNHO0FBRWxCLE1BQU1DLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUNDLDRFQUFDQztnQkFBS0MsV0FBVTs7a0NBQ2QsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUE2Qjs7Ozs7O29CQUMxQ0w7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibWFkLWNvbnNvbGUvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcydcclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiAnQk1hZCBDb25zb2xlJyxcclxuICBkZXNjcmlwdGlvbjogJ0xvY2FsIExMTSBjb25zb2xlIChPbGxhbWEpIHdpdGggR29vZ2xlIFNTTyBhbmQgTElORSB3ZWJob29rJ1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5PlxyXG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNFwiPlxyXG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+Qk1hZCBDb25zb2xlPC9oMT5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICA8L21haW4+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwibWFpbiIsImNsYXNzTmFtZSIsImgxIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/swr","vendor-chunks/use-sync-external-store"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CBMAD-METHOD-main%5Capps%5Cconsole&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();