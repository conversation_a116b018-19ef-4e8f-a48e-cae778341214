import { ProviderConfig, ProviderEntry } from './types'

export interface RouteContext {
  task?: string
  sensitivity?: 'low' | 'medium' | 'high'
  size?: 'small' | 'medium' | 'large'
  cost_priority?: 'low' | 'medium' | 'high'
  context_tokens_gt?: number
}

export function chooseProvider(config: ProviderConfig, ctx: RouteContext): ProviderEntry | undefined {
  const byId = new Map(config.providers.map(p => [p.id, p]))
  const rules = config.routing?.rules || []
  for (const r of rules) {
    if (!r.when) continue
    if (match(r.when, ctx)) {
      if (r.use && byId.has(r.use)) return byId.get(r.use)
      if (r.prefer && byId.has(r.prefer)) return byId.get(r.prefer)
      if (r.fallback && byId.has(r.fallback)) return byId.get(r.fallback)
    }
  }
  const def = config.routing?.default
  return def ? byId.get(def) : config.providers[0]
}

function match(when: Record<string, any>, ctx: Record<string, any>): boolean {
  return Object.entries(when).every(([k, v]) => {
    if (k === 'context_tokens_gt') {
      const n = Number(ctx['context_tokens_gt'] || 0)
      return n > Number(v)
    }
    return ctx[k] === v
  })
}
