{"$schema": "https://raw.githubusercontent.com/microsoft/vscode/master/src/vs/workbench/contrib/tasks/common/tasks.schema.json", "version": "2.0.0", "tasks": [{"label": "npm: build", "type": "npm", "script": "build", "group": "build", "problemMatcher": []}, {"label": "npm: build agents", "type": "npm", "script": "build:agents", "group": "build", "problemMatcher": []}, {"label": "npm: build teams", "type": "npm", "script": "build:teams", "group": "build", "problemMatcher": []}, {"label": "npm: validate", "type": "npm", "script": "validate", "group": "test", "problemMatcher": []}, {"label": "npm: flatten", "type": "npm", "script": "flatten", "group": "build", "problemMatcher": []}]}