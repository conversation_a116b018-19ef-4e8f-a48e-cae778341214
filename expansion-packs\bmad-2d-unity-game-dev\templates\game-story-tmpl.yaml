template:
  id: game-story-template-v3
  name: Game Development Story
  version: 3.0
  output:
    format: markdown
    filename: "stories/{{epic_name}}/{{story_id}}-{{story_name}}.md"
    title: "Story: {{story_title}}"

workflow:
  mode: interactive

sections:
  - id: initial-setup
    instruction: |
      This template creates detailed game development stories that are immediately actionable by game developers. Each story should focus on a single, implementable feature that contributes to the overall game functionality.
      
      Before starting, ensure you have access to:
      
      - Game Design Document (GDD)
      - Game Architecture Document
      - Any existing stories in this epic
      
      The story should be specific enough that a developer can implement it without requiring additional design decisions.

  - id: story-header
    content: |
      **Epic:** {{epic_name}}  
      **Story ID:** {{story_id}}  
      **Priority:** {{High|Medium|Low}}  
      **Points:** {{story_points}}  
      **Status:** Draft

  - id: description
    title: Description
    instruction: Provide a clear, concise description of what this story implements. Focus on the specific game feature or system being built. Reference the GDD section that defines this feature.
    template: "{{clear_description_of_what_needs_to_be_implemented}}"

  - id: acceptance-criteria
    title: Acceptance Criteria
    instruction: Define specific, testable conditions that must be met for the story to be considered complete. Each criterion should be verifiable and directly related to gameplay functionality.
    sections:
      - id: functional-requirements
        title: Functional Requirements
        type: checklist
        items:
          - "{{specific_functional_requirement}}"
      - id: technical-requirements
        title: Technical Requirements
        type: checklist
        items:
          - Code follows C# best practices
          - Maintains stable frame rate on target devices
          - No memory leaks or performance degradation
          - "{{specific_technical_requirement}}"
      - id: game-design-requirements
        title: Game Design Requirements
        type: checklist
        items:
          - "{{gameplay_requirement_from_gdd}}"
          - "{{balance_requirement_if_applicable}}"
          - "{{player_experience_requirement}}"

  - id: technical-specifications
    title: Technical Specifications
    instruction: Provide specific technical details that guide implementation. Include class names, file locations, and integration points based on the game architecture.
    sections:
      - id: files-to-modify
        title: Files to Create/Modify
        template: |
          **New Files:**
          
          - `{{file_path_1}}` - {{purpose}}
          - `{{file_path_2}}` - {{purpose}}
          
          **Modified Files:**
          
          - `{{existing_file_1}}` - {{changes_needed}}
          - `{{existing_file_2}}` - {{changes_needed}}
      - id: class-interface-definitions
        title: Class/Interface Definitions
        instruction: Define specific C# interfaces and class structures needed
        type: code
        language: c#
        template: |
          // {{interface_name}}
          public interface {{InterfaceName}}
          {
              {{type}} {{Property1}} { get; set; }
              {{return_type}} {{Method1}}({{params}});
          }

          // {{class_name}}
          public class {{ClassName}} : MonoBehaviour
          {
              private {{type}} _{{property}};

              private void Awake()
              {
                  // Implementation requirements
              }

              public {{return_type}} {{Method1}}({{params}})
              {
                  // Method requirements
              }
          }
      - id: integration-points
        title: Integration Points
        instruction: Specify how this feature integrates with existing systems
        template: |
          **Scene Integration:**

          - {{scene_name}}: {{integration_details}}

          **Component Dependencies:**

          - {{component_name}}: {{dependency_description}}

          **Event Communication:**

          - Emits: `{{event_name}}` when {{condition}}
          - Listens: `{{event_name}}` to {{response}}

  - id: implementation-tasks
    title: Implementation Tasks
    instruction: Break down the implementation into specific, ordered tasks. Each task should be completable in 1-4 hours.
    sections:
      - id: dev-agent-record
        title: Dev Agent Record
        template: |
          **Tasks:**

          - [ ] {{task_1_description}}
          - [ ] {{task_2_description}}
          - [ ] {{task_3_description}}
          - [ ] {{task_4_description}}
          - [ ] Write unit tests for {{component}}
          - [ ] Integration testing with {{related_system}}
          - [ ] Performance testing and optimization

          **Debug Log:**
          | Task | File | Change | Reverted? |
          |------|------|--------|-----------|
          | | | | |

          **Completion Notes:**

          <!-- Only note deviations from requirements, keep under 50 words -->

          **Change Log:**

          <!-- Only requirement changes during implementation -->

  - id: game-design-context
    title: Game Design Context
    instruction: Reference the specific sections of the GDD that this story implements
    template: |
      **GDD Reference:** {{section_name}} ({{page_or_section_number}})
      
      **Game Mechanic:** {{mechanic_name}}
      
      **Player Experience Goal:** {{experience_description}}
      
      **Balance Parameters:**
      
      - {{parameter_1}}: {{value_or_range}}
      - {{parameter_2}}: {{value_or_range}}

  - id: testing-requirements
    title: Testing Requirements
    instruction: Define specific testing criteria for this game feature
    sections:
      - id: unit-tests
        title: Unit Tests
        template: |
          **Test Files:**

          - `Assets/Tests/EditMode/{{component_name}}Tests.cs`

          **Test Scenarios:**

          - {{test_scenario_1}}
          - {{test_scenario_2}}
          - {{edge_case_test}}
      - id: game-testing
        title: Game Testing
        template: |
          **Manual Test Cases:**

          1. {{test_case_1_description}}

            - Expected: {{expected_behavior}}
            - Performance: {{performance_expectation}}

          2. {{test_case_2_description}}
            - Expected: {{expected_behavior}}
            - Edge Case: {{edge_case_handling}}
      - id: performance-tests
        title: Performance Tests
        template: |
          **Metrics to Verify:**

          - Frame rate maintains stable FPS
          - Memory usage stays under {{memory_limit}}MB
          - {{feature_specific_performance_metric}}

  - id: dependencies
    title: Dependencies
    instruction: List any dependencies that must be completed before this story can be implemented
    template: |
      **Story Dependencies:**
      
      - {{story_id}}: {{dependency_description}}
      
      **Technical Dependencies:**
      
      - {{system_or_file}}: {{requirement}}
      
      **Asset Dependencies:**
      
      - {{asset_type}}: {{asset_description}}
      - Location: `{{asset_path}}`

  - id: definition-of-done
    title: Definition of Done
    instruction: Checklist that must be completed before the story is considered finished
    type: checklist
    items:
      - All acceptance criteria met
      - Code reviewed and approved
      - Unit tests written and passing
      - Integration tests passing
      - Performance targets met
      - No C# compiler errors or warnings
      - Documentation updated
      - "{{game_specific_dod_item}}"

  - id: notes
    title: Notes
    instruction: Any additional context, design decisions, or implementation notes
    template: |
      **Implementation Notes:**
      
      - {{note_1}}
      - {{note_2}}
      
      **Design Decisions:**
      
      - {{decision_1}}: {{rationale}}
      - {{decision_2}}: {{rationale}}
      
      **Future Considerations:**
      
      - {{future_enhancement_1}}
      - {{future_optimization_1}}
