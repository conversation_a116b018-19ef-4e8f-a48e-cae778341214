import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

function verifyLineSignature(bodyRaw: string, signature: string | null, channelSecret: string) {
  const hmac = crypto.createHmac('sha256', channelSecret)
  hmac.update(bodyRaw)
  const expected = hmac.digest('base64')
  return signature === expected
}

export async function POST(req: NextRequest) {
  const channelSecret = process.env.LINE_CHANNEL_SECRET
  const accessToken = process.env.LINE_CHANNEL_ACCESS_TOKEN
  if (!channelSecret || !accessToken) {
    return NextResponse.json({ error: 'LINE not configured' }, { status: 400 })
  }

  const signature = req.headers.get('x-line-signature')
  const bodyRaw = await req.text()

  if (!verifyLineSignature(bodyRaw, signature, channelSecret)) {
    return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
  }

  const body = JSON.parse(bodyRaw)
  const events = body.events || []

  for (const ev of events) {
    if (ev.type === 'message' && ev.message?.type === 'text') {
      const userText = ev.message.text as string

      // Call local chat endpoint to get a reply
      const res = await fetch(`${process.env.APP_BASE_URL || 'http://localhost:3000'}/api/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: [{ role: 'user', content: userText }] })
      })
      const json = await res.json()
      const reply = json.reply || '（沒有回應）'

      // Respond to LINE
      await fetch('https://api.line.me/v2/bot/message/reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        },
        body: JSON.stringify({
          replyToken: ev.replyToken,
          messages: [{ type: 'text', text: reply }]
        })
      })
    }
  }

  return NextResponse.json({ ok: true })
}
