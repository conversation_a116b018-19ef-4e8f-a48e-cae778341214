import './globals.css'
import React from 'react'

export const metadata = {
  title: 'BMad Console',
  description: 'Local LLM console (Ollama) with Google SSO and LINE webhook'
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <main className="container mx-auto p-4">
          <h1 className="text-xl font-semibold mb-4">BMad Console</h1>
          {children}
        </main>
      </body>
    </html>
  )
}
