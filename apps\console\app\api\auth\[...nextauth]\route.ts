import NextAuth from 'next-auth'
import Google from 'next-auth/providers/google'
import { NextRequest, NextResponse } from 'next/server'

const allowedDomains = (process.env.ALLOWED_DOMAINS || 'your-company.com')
  .split(',')
  .map((s) => s.trim().toLowerCase())

const handler = NextAuth({
  providers: [
    Google({
      clientId: process.env.GOOGLE_OAUTH_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_OAUTH_CLIENT_SECRET!
    })
  ],
  callbacks: {
    async signIn({ profile }) {
      const email = (profile?.email || '').toLowerCase()
      const domain = email.split('@')[1]
      if (!email || !domain) return false
      return allowedDomains.includes(domain)
    }
  },
  trustHost: true
})

export { handler as GET, handler as POST }
