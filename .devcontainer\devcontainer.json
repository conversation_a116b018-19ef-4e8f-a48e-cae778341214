{"name": "bmad-method-dev", "image": "mcr.microsoft.com/devcontainers/base:bookworm", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "20"}, "ghcr.io/devcontainers/features/git:1": {}}, "remoteUser": "vscode", "customizations": {"vscode": {"settings": {"editor.formatOnSave": true, "files.eol": "\n", "prettier.requireConfig": false, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}, "extensions": ["esbenp.prettier-vscode", "redhat.vscode-yaml", "ms-azuretools.vscode-docker", "streetsidesoftware.code-spell-checker", "EditorConfig.EditorConfig"]}}, "postCreateCommand": "npm ci", "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}"}